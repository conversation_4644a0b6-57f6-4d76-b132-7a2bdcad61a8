class RegisterRequest {
  final String username;
  final String email;
  final String password;
  final String firstName;
  final String lastName;
  final List<String>? roles;

  RegisterRequest({
    required this.username,
    required this.email,
    required this.password,
    required this.firstName,
    required this.lastName,
    this.roles,
  });

  Map<String, dynamic> to<PERSON>son() {
    final Map<String, dynamic> data = {
      'username': username,
      'email': email,
      'password': password,
      'firstName': firstName,
      'lastName': lastName,
    };
    
    if (roles != null && roles!.isNotEmpty) {
      data['roles'] = roles;
    }
    
    return data;
  }
}