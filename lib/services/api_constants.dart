import 'dart:io' show Platform;
import 'package:flutter/foundation.dart' show kIsWeb;

class ApiConstants {
  // Base URL for the API - dynamically determined based on platform
  static String get baseUrl {
    if (kIsWeb) {
      // For web, use the window.location.hostname to get the current domain
      // This assumes the backend is running on the same host but different port
      return 'http://localhost:8080';
    } else if (Platform.isAndroid) {
      // For Android emulator to connect to host machine's localhost
      return 'http://********:8080';
    } else {
      // For desktop or other platforms
      return 'http://localhost:8080';
    }
  }

  // Authentication endpoints
  static const String registerEndpoint = '/api/auth/signup';
  static const String loginEndpoint = '/api/auth/login';
  static const String sessionInfoEndpoint = '/api/auth/session-info';
  static const String logoutEndpoint = '/api/auth/logout';

  // Profile endpoints
  static const String profileEndpoint = '/api/profile';
  static const String changePasswordEndpoint = '/api/profile/change-password';

  // Admin endpoints
  static const String adminVendorsEndpoint = '/api/admin/usuarios/vendedores';
  static const String adminUsersEndpoint = '/api/admin/usuarios';

  // Token key for secure storage
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_data';
}
